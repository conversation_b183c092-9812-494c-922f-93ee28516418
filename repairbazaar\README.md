# RepairBazaar 🔧

**Netflix-inspired Mobile Repair Shop Management App**

A fully-offline mobile repair shop management application built with Flutter and SQLite, featuring a cinematic dark theme inspired by Netflix's UI design.

## ✨ Features

### 🎬 Netflix-Inspired UI
- **Cinematic Dark Theme**: Deep black/charcoal background with high-contrast accents
- **Netflix-Style Cards**: Large immersive thumbnails with hover effects and quick action overlays
- **Horizontal Carousels**: Smooth browsing experience for jobs, inventory, and technicians
- **Hero Banners**: Eye-catching alerts and statistics display
- **Smooth Animations**: Subtle micro-animations optimized for performance

### 📱 Core Functionality
- **Job Management**: Complete repair order lifecycle with multi-stage status tracking
- **Customer Management**: Customer profiles with repair history
- **Inventory Management**: Parts tracking with auto-deduct and low stock alerts
- **Technician Management**: Workload tracking and assignment system
- **Billing & Invoicing**: Offline PDF generation and printing
- **Search & IMEI Lookup**: Fast search across jobs, customers, and devices
- **Analytics & Reports**: Business insights and performance metrics

### 🔧 Technical Features
- **Fully Offline**: No internet required - all data stored locally
- **SQLite Database**: Robust local data storage with Drift ORM
- **Real-time Updates**: Reactive UI with Riverpod state management
- **WhatsApp Integration**: Direct customer communication
- **Local Notifications**: Job status and reminder alerts
- **Data Backup/Restore**: Local database backup functionality
- **PWA Ready**: Prepared for future web deployment

## 🏗️ Architecture

### Database Schema
```
├── customers (id, name, phone, email, address)
├── technicians (id, name, phone, specialization, is_active)
├── inventory (id, part_name, category, quantity, prices)
├── jobs (id, job_number, customer_id, device_info, status, timing)
├── job_parts (id, job_id, part_id, quantity_used, prices)
├── payments (id, job_id, amount, payment_method, transaction_id)
├── job_status_history (id, job_id, status_changes, timestamps)
└── device_photos (id, job_id, photo_path, photo_type)
```

### Tech Stack
- **Frontend**: Flutter (latest stable)
- **Database**: SQLite with Drift ORM
- **State Management**: Riverpod with code generation
- **PDF Generation**: pdf + printing packages
- **Notifications**: flutter_local_notifications
- **External Communication**: url_launcher (WhatsApp)
- **Charts**: fl_chart for analytics
- **Image Handling**: image_picker for device photos

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Android Studio / VS Code
- Android device or emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd repairbazaar
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   dart run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Build for Release

```bash
# Android APK
flutter build apk --release

# Android App Bundle
flutter build appbundle --release
```

## 📱 Navigation Structure

### Bottom Navigation (5 Tabs)
1. **Home** - Dashboard with quick stats and active repairs
2. **Orders** - Job management and tracking
3. **Inventory** - Parts and stock management
4. **Customers** - Customer profiles and history
5. **Reports** - Analytics and business insights

### Top App Bar
- **Left**: Brand logo and app name
- **Center**: Global search (jobs, customers, IMEI)
- **Right**: WhatsApp quick action, notifications, menu

## 🎨 Design System

### Color Palette
- **Primary Black**: `#000000` (Netflix-style background)
- **Charcoal**: `#141414` (Card backgrounds)
- **Netflix Red**: `#E50914` (Primary accent)
- **Electric Blue**: `#00D4FF` (Secondary accent)
- **Status Colors**: Green (completed), Amber (pending), Red (cancelled)

### Typography
- **Display**: Large headings with bold weights
- **Headlines**: Section headers with medium weights
- **Body**: Regular content with optimal readability
- **Labels**: Small text for badges and metadata

## 📊 Key Screens

### Home Dashboard
- Welcome hero banner with quick actions
- Stats cards (pending jobs, today's earnings, completed jobs)
- Active repairs carousel with Netflix-style job cards
- Low stock alerts and technician highlights

### Job Management
- Netflix-tile job cards with device thumbnails
- Quick action overlays (Complete, Assign, Message)
- Multi-stage status workflow
- Device photo documentation

### Inventory Management
- Category-based organization
- Low stock alerts and reorder suggestions
- Auto-deduct on job completion
- Supplier and pricing management

## 🔧 Configuration

### Android Setup
The app requires Android NDK 27.0.12077973 and core library desugaring. These are already configured in `android/app/build.gradle.kts`.

### Database Initialization
The app automatically creates sample data on first run:
- Default technician (Shop Owner)
- Sample inventory items
- Example device categories

## 📝 Development

### Code Generation
When modifying database tables or Riverpod providers:
```bash
dart run build_runner build --delete-conflicting-outputs
```

### Adding New Features
1. Update database schema in `lib/database/tables.dart`
2. Add providers in `lib/providers/`
3. Create UI components in `lib/widgets/`
4. Implement screens in `lib/screens/`

### Testing
```bash
# Run tests
flutter test

# Run with coverage
flutter test --coverage
```

## 🚀 Deployment

### Android
1. Update version in `pubspec.yaml`
2. Build release APK: `flutter build apk --release`
3. Sign and distribute through Google Play or direct installation

### Future Web Support
The app is designed to be PWA-ready for future web deployment with minimal modifications.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and ensure code quality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Netflix for UI/UX inspiration
- Flutter team for the amazing framework
- Drift team for the excellent SQLite ORM
- Riverpod team for state management

---

**Built with ❤️ for mobile repair shop owners**

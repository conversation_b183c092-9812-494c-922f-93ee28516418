import 'package:drift/drift.dart';

// Customers table
class Customers extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 100)();
  TextColumn get phone => text().withLength(min: 10, max: 15)();
  TextColumn get email => text().nullable()();
  TextColumn get address => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// Technicians table
class Technicians extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().withLength(min: 1, max: 100)();
  TextColumn get phone => text().withLength(min: 10, max: 15)();
  TextColumn get email => text().nullable()();
  TextColumn get specialization => text().nullable()(); // e.g., "iPhone", "Android", "Laptop"
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// Inventory/Parts table
class Inventory extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get partName => text().withLength(min: 1, max: 200)();
  TextColumn get partNumber => text().nullable()(); // SKU or part number
  TextColumn get category => text()(); // e.g., "Screen", "Battery", "Camera"
  TextColumn get brand => text().nullable()(); // e.g., "Apple", "Samsung"
  TextColumn get model => text().nullable()(); // Compatible device models
  IntColumn get quantity => integer().withDefault(const Constant(0))();
  IntColumn get minStockLevel => integer().withDefault(const Constant(5))();
  RealColumn get costPrice => real()(); // Purchase price
  RealColumn get sellingPrice => real()(); // Selling price
  TextColumn get supplier => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// Jobs/Repair Orders table
class Jobs extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get jobNumber => text().unique()(); // Auto-generated job number
  IntColumn get customerId => integer().references(Customers, #id)();
  IntColumn get technicianId => integer().nullable().references(Technicians, #id)();
  
  // Device Information
  TextColumn get deviceType => text()(); // "Phone", "Tablet", "Laptop"
  TextColumn get deviceBrand => text()(); // "Apple", "Samsung", etc.
  TextColumn get deviceModel => text()(); // "iPhone 14", "Galaxy S23"
  TextColumn get imei => text().nullable()();
  TextColumn get serialNumber => text().nullable()();
  
  // Job Details
  TextColumn get issueDescription => text()();
  TextColumn get diagnosis => text().nullable()();
  TextColumn get repairNotes => text().nullable()();
  
  // Status and Timing
  TextColumn get status => text().withDefault(const Constant('pending'))(); 
  // Status: pending, diagnosed, in_progress, waiting_parts, completed, delivered, cancelled
  DateTimeColumn get receivedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get estimatedCompletion => dateTime().nullable()();
  DateTimeColumn get completedAt => dateTime().nullable()();
  DateTimeColumn get deliveredAt => dateTime().nullable()();
  
  // Pricing
  RealColumn get laborCost => real().withDefault(const Constant(0.0))();
  RealColumn get totalCost => real().withDefault(const Constant(0.0))();
  BoolColumn get isPaid => boolean().withDefault(const Constant(false))();
  
  // Additional Info
  TextColumn get customerPassword => text().nullable()(); // Device unlock password
  TextColumn get accessories => text().nullable()(); // "Charger, Case, etc."
  TextColumn get deviceCondition => text().nullable()(); // Physical condition notes
  
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}

// Job Parts - Junction table for parts used in jobs
class JobParts extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get jobId => integer().references(Jobs, #id)();
  IntColumn get partId => integer().references(Inventory, #id)();
  IntColumn get quantityUsed => integer()();
  RealColumn get unitPrice => real()(); // Price at time of use
  RealColumn get totalPrice => real()(); // quantityUsed * unitPrice
  DateTimeColumn get usedAt => dateTime().withDefault(currentDateAndTime)();
}

// Payments table
class Payments extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get jobId => integer().references(Jobs, #id)();
  RealColumn get amount => real()();
  TextColumn get paymentMethod => text()(); // "cash", "card", "upi", "bank_transfer"
  TextColumn get transactionId => text().nullable()(); // For digital payments
  TextColumn get notes => text().nullable()();
  DateTimeColumn get paidAt => dateTime().withDefault(currentDateAndTime)();
}

// Job Status History - Track status changes
class JobStatusHistory extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get jobId => integer().references(Jobs, #id)();
  TextColumn get fromStatus => text().nullable()();
  TextColumn get toStatus => text()();
  TextColumn get notes => text().nullable()();
  IntColumn get changedBy => integer().nullable().references(Technicians, #id)();
  DateTimeColumn get changedAt => dateTime().withDefault(currentDateAndTime)();
}

// Device Photos - Store device condition photos
class DevicePhotos extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get jobId => integer().references(Jobs, #id)();
  TextColumn get photoPath => text()(); // Local file path
  TextColumn get photoType => text()(); // "before", "during", "after"
  TextColumn get description => text().nullable()();
  DateTimeColumn get takenAt => dateTime().withDefault(currentDateAndTime)();
}

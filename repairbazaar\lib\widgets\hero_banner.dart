import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

class HeroBanner extends StatefulWidget {
  final String title;
  final String? subtitle;
  final String? description;
  final Widget? backgroundImage;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final double height;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Gradient? gradient;
  final Color? backgroundColor;

  const HeroBanner({
    super.key,
    required this.title,
    this.subtitle,
    this.description,
    this.backgroundImage,
    this.actions,
    this.onTap,
    this.height = 200,
    this.margin,
    this.padding,
    this.gradient,
    this.backgroundColor,
  });

  @override
  State<HeroBanner> createState() => _HeroBannerState();
}

class _HeroBannerState extends State<HeroBanner>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      margin: widget.margin ?? const EdgeInsets.all(16),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: GestureDetector(
                onTap: widget.onTap,
                onTapDown: widget.onTap != null ? _onTapDown : null,
                onTapUp: widget.onTap != null ? _onTapUp : null,
                onTapCancel: widget.onTap != null ? _onTapCancel : null,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    gradient: widget.gradient ?? AppColors.redGradient,
                    color: widget.backgroundColor,
                    boxShadow: AppColors.createCardShadow(),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Stack(
                      children: [
                        // Background image
                        if (widget.backgroundImage != null)
                          Positioned.fill(
                            child: widget.backgroundImage!,
                          ),
                        
                        // Gradient overlay
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: widget.gradient ?? 
                                  const LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Color(0x40000000),
                                      Color(0x80000000),
                                    ],
                                  ),
                            ),
                          ),
                        ),
                        
                        // Content
                        Positioned.fill(
                          child: Container(
                            padding: widget.padding ?? const EdgeInsets.all(24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Subtitle
                                if (widget.subtitle != null) ...[
                                  Text(
                                    widget.subtitle!,
                                    style: AppTypography.labelLarge.copyWith(
                                      color: AppColors.primaryText.withOpacity(0.8),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                ],
                                
                                // Title
                                Text(
                                  widget.title,
                                  style: AppTypography.heroTitle,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                
                                // Description
                                if (widget.description != null) ...[
                                  const SizedBox(height: 12),
                                  Text(
                                    widget.description!,
                                    style: AppTypography.bodyMedium.copyWith(
                                      color: AppColors.primaryText.withOpacity(0.9),
                                    ),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                                
                                // Actions
                                if (widget.actions != null) ...[
                                  const SizedBox(height: 20),
                                  Row(
                                    children: widget.actions!,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Specialized hero banners for different use cases
class AlertHeroBanner extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;
  final VoidCallback? onAction;
  final String? actionLabel;

  const AlertHeroBanner({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    this.onAction,
    this.actionLabel,
  });

  @override
  Widget build(BuildContext context) {
    return HeroBanner(
      title: title,
      description: description,
      height: 160,
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          AppColors.warningAmber,
          Color(0xFFFF8F00),
        ],
      ),
      actions: [
        Container(
          decoration: const BoxDecoration(
            color: AppColors.primaryText,
            shape: BoxShape.circle,
          ),
          child: IconButton(
            onPressed: onAction,
            icon: Icon(icon),
            color: AppColors.warningAmber,
          ),
        ),
        if (actionLabel != null) ...[
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: onAction,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryText,
              foregroundColor: AppColors.warningAmber,
            ),
            child: Text(actionLabel!),
          ),
        ],
      ],
    );
  }
}

class StatsHeroBanner extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;

  const StatsHeroBanner({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final bannerColor = color ?? AppColors.netflixRed;
    
    return HeroBanner(
      title: value,
      subtitle: title,
      description: subtitle,
      height: 140,
      onTap: onTap,
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          bannerColor,
          bannerColor.withOpacity(0.8),
        ],
      ),
      actions: [
        Container(
          decoration: BoxDecoration(
            color: AppColors.primaryText.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          padding: const EdgeInsets.all(12),
          child: Icon(
            icon,
            color: AppColors.primaryText,
            size: 24,
          ),
        ),
      ],
    );
  }
}

class WelcomeHeroBanner extends StatelessWidget {
  final String userName;
  final String? message;
  final VoidCallback? onQuickAction;

  const WelcomeHeroBanner({
    super.key,
    required this.userName,
    this.message,
    this.onQuickAction,
  });

  @override
  Widget build(BuildContext context) {
    final hour = DateTime.now().hour;
    String greeting;
    
    if (hour < 12) {
      greeting = 'Good Morning';
    } else if (hour < 17) {
      greeting = 'Good Afternoon';
    } else {
      greeting = 'Good Evening';
    }

    return HeroBanner(
      title: '$greeting, $userName',
      description: message ?? 'Ready to manage your repair shop?',
      height: 180,
      gradient: AppColors.cardGradient,
      actions: [
        if (onQuickAction != null)
          ElevatedButton.icon(
            onPressed: onQuickAction,
            icon: const Icon(Icons.add),
            label: const Text('New Job'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.netflixRed,
              foregroundColor: AppColors.primaryText,
            ),
          ),
      ],
    );
  }
}

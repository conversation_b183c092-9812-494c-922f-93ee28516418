import 'package:flutter/material.dart';
import 'app_colors.dart';

class AppTypography {
  // Netflix-inspired typography system
  
  // Font families
  static const String primaryFont = 'Roboto';
  static const String displayFont = 'Roboto';
  
  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;
  
  // Display styles (for hero sections and large headings)
  static const TextStyle displayLarge = TextStyle(
    fontFamily: displayFont,
    fontSize: 48.0,
    fontWeight: extraBold,
    color: AppColors.primaryText,
    height: 1.1,
    letterSpacing: -1.0,
  );
  
  static const TextStyle displayMedium = TextStyle(
    fontFamily: displayFont,
    fontSize: 36.0,
    fontWeight: bold,
    color: AppColors.primaryText,
    height: 1.2,
    letterSpacing: -0.5,
  );
  
  static const TextStyle displaySmall = TextStyle(
    fontFamily: displayFont,
    fontSize: 28.0,
    fontWeight: bold,
    color: AppColors.primaryText,
    height: 1.3,
    letterSpacing: -0.25,
  );
  
  // Headline styles (for section headers)
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 24.0,
    fontWeight: semiBold,
    color: AppColors.primaryText,
    height: 1.3,
    letterSpacing: 0.0,
  );
  
  static const TextStyle headlineMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 20.0,
    fontWeight: semiBold,
    color: AppColors.primaryText,
    height: 1.4,
    letterSpacing: 0.0,
  );
  
  static const TextStyle headlineSmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: 18.0,
    fontWeight: medium,
    color: AppColors.primaryText,
    height: 1.4,
    letterSpacing: 0.0,
  );
  
  // Title styles (for card titles and important labels)
  static const TextStyle titleLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 16.0,
    fontWeight: semiBold,
    color: AppColors.primaryText,
    height: 1.5,
    letterSpacing: 0.1,
  );
  
  static const TextStyle titleMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14.0,
    fontWeight: medium,
    color: AppColors.primaryText,
    height: 1.5,
    letterSpacing: 0.1,
  );
  
  static const TextStyle titleSmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12.0,
    fontWeight: medium,
    color: AppColors.primaryText,
    height: 1.5,
    letterSpacing: 0.2,
  );
  
  // Body styles (for regular content)
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 16.0,
    fontWeight: regular,
    color: AppColors.primaryText,
    height: 1.6,
    letterSpacing: 0.0,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14.0,
    fontWeight: regular,
    color: AppColors.primaryText,
    height: 1.6,
    letterSpacing: 0.0,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12.0,
    fontWeight: regular,
    color: AppColors.secondaryText,
    height: 1.6,
    letterSpacing: 0.1,
  );
  
  // Label styles (for buttons, chips, and small labels)
  static const TextStyle labelLarge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14.0,
    fontWeight: medium,
    color: AppColors.primaryText,
    height: 1.4,
    letterSpacing: 0.2,
  );
  
  static const TextStyle labelMedium = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12.0,
    fontWeight: medium,
    color: AppColors.primaryText,
    height: 1.4,
    letterSpacing: 0.3,
  );
  
  static const TextStyle labelSmall = TextStyle(
    fontFamily: primaryFont,
    fontSize: 10.0,
    fontWeight: medium,
    color: AppColors.secondaryText,
    height: 1.4,
    letterSpacing: 0.4,
  );
  
  // Special styles for specific use cases
  static const TextStyle heroTitle = TextStyle(
    fontFamily: displayFont,
    fontSize: 32.0,
    fontWeight: extraBold,
    color: AppColors.primaryText,
    height: 1.2,
    letterSpacing: -0.5,
    shadows: [
      Shadow(
        color: AppColors.shadowColor,
        offset: Offset(0, 2),
        blurRadius: 4,
      ),
    ],
  );
  
  static const TextStyle cardTitle = TextStyle(
    fontFamily: primaryFont,
    fontSize: 16.0,
    fontWeight: semiBold,
    color: AppColors.primaryText,
    height: 1.3,
    letterSpacing: 0.0,
  );
  
  static const TextStyle cardSubtitle = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14.0,
    fontWeight: regular,
    color: AppColors.secondaryText,
    height: 1.4,
    letterSpacing: 0.0,
  );
  
  static const TextStyle statusBadge = TextStyle(
    fontFamily: primaryFont,
    fontSize: 11.0,
    fontWeight: semiBold,
    color: AppColors.primaryText,
    height: 1.2,
    letterSpacing: 0.5,
  );
  
  static const TextStyle navigationLabel = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12.0,
    fontWeight: medium,
    color: AppColors.secondaryText,
    height: 1.2,
    letterSpacing: 0.3,
  );
  
  static const TextStyle buttonText = TextStyle(
    fontFamily: primaryFont,
    fontSize: 14.0,
    fontWeight: semiBold,
    color: AppColors.primaryText,
    height: 1.2,
    letterSpacing: 0.2,
  );
  
  static const TextStyle inputText = TextStyle(
    fontFamily: primaryFont,
    fontSize: 16.0,
    fontWeight: regular,
    color: AppColors.primaryText,
    height: 1.5,
    letterSpacing: 0.0,
  );
  
  static const TextStyle inputLabel = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12.0,
    fontWeight: medium,
    color: AppColors.secondaryText,
    height: 1.4,
    letterSpacing: 0.2,
  );
  
  static const TextStyle errorText = TextStyle(
    fontFamily: primaryFont,
    fontSize: 12.0,
    fontWeight: regular,
    color: AppColors.errorRed,
    height: 1.4,
    letterSpacing: 0.1,
  );
  
  // Helper methods for dynamic text styles
  static TextStyle getStatusTextStyle(String status) {
    return statusBadge.copyWith(
      color: AppColors.getStatusColor(status),
    );
  }
  
  static TextStyle getCategoryTextStyle(String category) {
    return labelMedium.copyWith(
      color: AppColors.getCategoryColor(category),
    );
  }
  
  static TextStyle getDeviceTypeTextStyle(String deviceType) {
    return labelMedium.copyWith(
      color: AppColors.getDeviceTypeColor(deviceType),
    );
  }
  
  // Text theme for Material Theme
  static const TextTheme textTheme = TextTheme(
    displayLarge: displayLarge,
    displayMedium: displayMedium,
    displaySmall: displaySmall,
    headlineLarge: headlineLarge,
    headlineMedium: headlineMedium,
    headlineSmall: headlineSmall,
    titleLarge: titleLarge,
    titleMedium: titleMedium,
    titleSmall: titleSmall,
    bodyLarge: bodyLarge,
    bodyMedium: bodyMedium,
    bodySmall: bodySmall,
    labelLarge: labelLarge,
    labelMedium: labelMedium,
    labelSmall: labelSmall,
  );
}

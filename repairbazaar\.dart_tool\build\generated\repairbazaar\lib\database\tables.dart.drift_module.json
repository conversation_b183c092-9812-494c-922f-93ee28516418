{"elements": {"customers": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "customers"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 61, "name": "Customers"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 103, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 155, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 1, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "phone", "nameInDart": "phone", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 219, "name": "phone"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 10, "max_length": 15}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "email", "nameInDart": "email", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 284, "name": "email"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "address", "nameInDart": "address", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 331, "name": "address"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 384, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 464, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Customers", "row_class_name": "Customer", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "technicians": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "technicians"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 553, "name": "Technicians"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 597, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "name", "nameInDart": "name", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 649, "name": "name"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 1, "max_length": 100}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "phone", "nameInDart": "phone", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 713, "name": "phone"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 10, "max_length": 15}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "email", "nameInDart": "email", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 778, "name": "email"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "specialization", "nameInDart": "specialization", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 825, "name": "specialization"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_active", "nameInDart": "isActive", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 920, "name": "isActive"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(true)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1000, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1080, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Technicians", "row_class_name": "Technician", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "inventory": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "inventory"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1173, "name": "Inventory"}, "references": [], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1215, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "part_name", "nameInDart": "partName", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1267, "name": "partName"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "limit_text_length", "min_length": 1, "max_length": 200}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "part_number", "nameInDart": "partNumber", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1335, "name": "partNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "category", "nameInDart": "category", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1409, "name": "category"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "brand", "nameInDart": "brand", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1487, "name": "brand"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "model", "nameInDart": "model", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1562, "name": "model"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "quantity", "nameInDart": "quantity", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1636, "name": "quantity"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "min_stock_level", "nameInDart": "minStockLevel", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1708, "name": "minStockLevel"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(5)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "cost_price", "nameInDart": "costPrice", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1786, "name": "costPrice"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "selling_price", "nameInDart": "sellingPrice", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1844, "name": "sellingPrice"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "supplier", "nameInDart": "supplier", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1904, "name": "supplier"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 1958, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2038, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Inventory", "row_class_name": "InventoryData", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "jobs": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2134, "name": "Jobs"}, "references": [{"library_uri": "package:repairbazaar/database/tables.dart", "name": "customers"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "technicians"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2171, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "job_number", "nameInDart": "jobNumber", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2223, "name": "jobNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "unique"}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "customer_id", "nameInDart": "customerId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2300, "name": "customerId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "customers"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "technician_id", "nameInDart": "technicianId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2370, "name": "technicianId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "technicians"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "device_type", "nameInDart": "deviceType", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2483, "name": "deviceType"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "device_brand", "nameInDart": "deviceBrand", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2555, "name": "deviceBrand"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "device_model", "nameInDart": "deviceModel", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2625, "name": "deviceModel"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "imei", "nameInDart": "imei", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2696, "name": "imei"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "serial_number", "nameInDart": "serialNumber", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2742, "name": "serialNumber"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "issue_description", "nameInDart": "issueDescription", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2816, "name": "issueDescription"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "diagnosis", "nameInDart": "diagnosis", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2863, "name": "diagnosis"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "repair_notes", "nameInDart": "repairNotes", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2914, "name": "repairNotes"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "status", "nameInDart": "status", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 2993, "name": "status"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "('pending')"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "received_at", "nameInDart": "receivedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3167, "name": "receivedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "estimated_completion", "nameInDart": "estimatedCompletion", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3248, "name": "estimatedCompletion"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "completed_at", "nameInDart": "completedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3317, "name": "completedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": true, "nameInSql": "delivered_at", "nameInDart": "deliveredAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3378, "name": "deliveredAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "labor_cost", "nameInDart": "laborCost", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3451, "name": "laborCost"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0.0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "total_cost", "nameInDart": "totalCost", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3524, "name": "totalCost"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(0.0)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "bool"}, "nullable": false, "nameInSql": "is_paid", "nameInDart": "isPaid", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3597, "name": "isPaid"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": ["const ", {"lexeme": "Constant", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}, "(false)"]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "customer_password", "nameInDart": "customerPassword", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3696, "name": "customerPassword"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "accessories", "nameInDart": "accessories", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3780, "name": "accessories"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "device_condition", "nameInDart": "deviceCondition", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3858, "name": "deviceCondition"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "created_at", "nameInDart": "createdAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 3950, "name": "createdAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "updated_at", "nameInDart": "updatedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4030, "name": "updatedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Jobs", "row_class_name": "Job", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "job_parts": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "job_parts"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4151, "name": "JobParts"}, "references": [{"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "inventory"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4192, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "job_id", "nameInDart": "jobId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4243, "name": "jobId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "part_id", "nameInDart": "partId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4303, "name": "partId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "inventory"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "quantity_used", "nameInDart": "quantityUsed", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4369, "name": "quantityUsed"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "unit_price", "nameInDart": "unitPrice", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4415, "name": "unitPrice"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "total_price", "nameInDart": "totalPrice", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4479, "name": "totalPrice"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "used_at", "nameInDart": "usedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4552, "name": "usedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "JobParts", "row_class_name": "JobPart", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "payments": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "payments"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4635, "name": "Payments"}, "references": [{"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4676, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "job_id", "nameInDart": "jobId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4727, "name": "jobId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "double"}, "nullable": false, "nameInSql": "amount", "nameInDart": "amount", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4788, "name": "amount"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "payment_method", "nameInDart": "paymentMethod", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4825, "name": "paymentMethod"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "transaction_id", "nameInDart": "transactionId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4911, "name": "transactionId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "notes", "nameInDart": "notes", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 4990, "name": "notes"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "paid_at", "nameInDart": "paidAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5041, "name": "paidAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "Payments", "row_class_name": "Payment", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "job_status_history": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "job_status_history"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5151, "name": "JobStatusHistory"}, "references": [{"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "technicians"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5200, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "job_id", "nameInDart": "jobId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5251, "name": "jobId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "from_status", "nameInDart": "fromStatus", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5312, "name": "fromStatus"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "to_status", "nameInDart": "to<PERSON><PERSON><PERSON>", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5364, "name": "to<PERSON><PERSON><PERSON>"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "notes", "nameInDart": "notes", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5403, "name": "notes"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": true, "nameInSql": "changed_by", "nameInDart": "changedBy", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5449, "name": "changedBy"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "technicians"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "changed_at", "nameInDart": "changedAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5536, "name": "changedAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "JobStatusHistory", "row_class_name": "JobStatusHistoryData", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}, "device_photos": {"id": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "device_photos"}, "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5653, "name": "DevicePhotos"}, "references": [{"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}], "type": "table", "columns": [{"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "id", "nameInDart": "id", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5698, "name": "id"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "primary", "is_auto_increment": true}], "customConstraints": null}, {"sqlType": {"builtin": "int"}, "nullable": false, "nameInSql": "job_id", "nameInDart": "jobId", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5749, "name": "jobId"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [{"type": "foreign_key", "column": {"table": {"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, "name": "id"}, "onUpdate": null, "onDelete": null, "initiallyDeferred": false}], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "photo_path", "nameInDart": "photoPath", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5810, "name": "photoPath"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": false, "nameInSql": "photo_type", "nameInDart": "photoType", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5869, "name": "photoType"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "string"}, "nullable": true, "nameInSql": "description", "nameInDart": "description", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5940, "name": "description"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": null, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}, {"sqlType": {"builtin": "dateTime"}, "nullable": false, "nameInSql": "taken_at", "nameInDart": "takenAt", "declaration": {"source_uri": "package:repairbazaar/database/tables.dart", "offset": 5997, "name": "takenAt"}, "typeConverter": null, "clientDefaultCode": null, "defaultArgument": {"elements": [{"lexeme": "currentDateAndTime", "import_uri": "package:drift/src/runtime/query_builder/query_builder.dart"}]}, "overriddenJsonName": null, "referenceName": null, "documentationComment": null, "constraints": [], "customConstraints": null}], "existing_data_class": null, "table_constraints": [], "custom_parent_class": null, "interfaces_for_row_class": [], "fixed_entity_info_name": null, "base_dart_name": "DevicePhotos", "row_class_name": "DevicePhoto", "companion_class_name": null, "without_rowid": false, "strict": false, "write_default_constraints": true, "custom_constraints": [], "attached_indices": []}}, "imports": ["package:drift/drift.dart"]}
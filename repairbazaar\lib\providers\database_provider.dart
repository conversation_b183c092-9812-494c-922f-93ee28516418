import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../database/database.dart';

// Database provider - singleton instance
final databaseProvider = Provider<RepairDatabase>((ref) {
  final database = RepairDatabase();
  
  // Ensure database is closed when provider is disposed
  ref.onDispose(() {
    database.close();
  });
  
  return database;
});

// Customer providers
final customersProvider = StreamProvider<List<Customer>>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.customers).watch();
});

final customerByIdProvider = StreamProvider.family<Customer?, int>((ref, id) {
  final database = ref.watch(databaseProvider);
  return (database.select(database.customers)..where((c) => c.id.equals(id))).watchSingleOrNull();
});

// Technician providers
final techniciansProvider = StreamProvider<List<Technician>>((ref) {
  final database = ref.watch(databaseProvider);
  return (database.select(database.technicians)..where((t) => t.isActive.equals(true))).watch();
});

// Inventory providers
final inventoryProvider = StreamProvider<List<InventoryData>>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.inventory).watch();
});

final lowStockItemsProvider = StreamProvider<List<InventoryData>>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.inventory).watch().map((items) {
    return items.where((item) => item.quantity <= item.minStockLevel).toList();
  });
});

// Job providers
final allJobsProvider = StreamProvider<List<Job>>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.jobs).watch().map((jobs) {
    jobs.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return jobs;
  });
});

final jobsByStatusProvider = StreamProvider.family<List<Job>, String>((ref, status) {
  final database = ref.watch(databaseProvider);
  return database.select(database.jobs).watch().map((jobs) {
    final filtered = jobs.where((job) => job.status == status).toList();
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return filtered;
  });
});

final jobByIdProvider = StreamProvider.family<Job?, int>((ref, id) {
  final database = ref.watch(databaseProvider);
  return database.select(database.jobs).watch().map((jobs) {
    try {
      return jobs.firstWhere((job) => job.id == id);
    } catch (e) {
      return null;
    }
  });
});

final activeJobsProvider = StreamProvider<List<Job>>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.jobs).watch().map((jobs) {
    final activeStatuses = ['pending', 'diagnosed', 'in_progress', 'waiting_parts'];
    final filtered = jobs.where((job) => activeStatuses.contains(job.status)).toList();
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return filtered;
  });
});

// Analytics providers - simplified for now
final todaysEarningsProvider = StreamProvider<double>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.payments).watch().map((payments) {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final todaysPayments = payments.where((payment) =>
        payment.paidAt.isAfter(startOfDay) && payment.paidAt.isBefore(endOfDay));

    return todaysPayments.fold<double>(0.0, (sum, payment) => sum + payment.amount);
  });
});

final todaysCompletedJobsProvider = StreamProvider<int>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.jobs).watch().map((jobs) {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    final todaysCompleted = jobs.where((job) =>
        job.completedAt != null &&
        job.completedAt!.isAfter(startOfDay) &&
        job.completedAt!.isBefore(endOfDay));

    return todaysCompleted.length;
  });
});

final pendingJobsCountProvider = StreamProvider<int>((ref) {
  final database = ref.watch(databaseProvider);
  return database.select(database.jobs).watch().map((jobs) {
    final activeStatuses = ['pending', 'diagnosed', 'in_progress', 'waiting_parts'];
    return jobs.where((job) => activeStatuses.contains(job.status)).length;
  });
});

// Search providers
final customerSearchProvider = StreamProvider.family<List<Customer>, String>((ref, query) {
  if (query.isEmpty) return Stream.value([]);

  final database = ref.watch(databaseProvider);
  return database.select(database.customers).watch().map((customers) {
    final lowerQuery = query.toLowerCase();
    return customers.where((customer) =>
        customer.name.toLowerCase().contains(lowerQuery) ||
        customer.phone.contains(query)).toList();
  });
});

final inventorySearchProvider = StreamProvider.family<List<InventoryData>, String>((ref, query) {
  if (query.isEmpty) return Stream.value([]);

  final database = ref.watch(databaseProvider);
  return database.select(database.inventory).watch().map((items) {
    final lowerQuery = query.toLowerCase();
    return items.where((item) =>
        item.partName.toLowerCase().contains(lowerQuery) ||
        item.category.toLowerCase().contains(lowerQuery) ||
        (item.brand?.toLowerCase().contains(lowerQuery) ?? false)).toList();
  });
});

final jobSearchProvider = StreamProvider.family<List<Job>, String>((ref, query) {
  if (query.isEmpty) return Stream.value([]);

  final database = ref.watch(databaseProvider);
  return database.select(database.jobs).watch().map((jobs) {
    final lowerQuery = query.toLowerCase();
    return jobs.where((job) =>
        job.jobNumber.toLowerCase().contains(lowerQuery) ||
        job.deviceModel.toLowerCase().contains(lowerQuery) ||
        (job.imei?.toLowerCase().contains(lowerQuery) ?? false)).toList();
  });
});

// Job parts provider
final jobPartsProvider = StreamProvider.family<List<JobPart>, int>((ref, jobId) {
  final database = ref.watch(databaseProvider);
  return (database.select(database.jobParts)..where((jp) => jp.jobId.equals(jobId))).watch();
});

// Job payments provider
final jobPaymentsProvider = StreamProvider.family<List<Payment>, int>((ref, jobId) {
  final database = ref.watch(databaseProvider);
  return (database.select(database.payments)..where((p) => p.jobId.equals(jobId))).watch();
});

{"valid_import": true, "imports": [{"uri": "package:drift/drift.dart", "transitive": false}, {"uri": "package:drift/native.dart", "transitive": false}, {"uri": "package:path_provider/path_provider.dart", "transitive": false}, {"uri": "package:path/path.dart", "transitive": false}, {"uri": "package:sqlite3/sqlite3.dart", "transitive": false}, {"uri": "package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart", "transitive": false}, {"uri": "package:repairbazaar/database/tables.dart", "transitive": false}], "elements": [{"kind": "database", "name": "RepairDatabase", "dart_name": "RepairDatabase"}]}
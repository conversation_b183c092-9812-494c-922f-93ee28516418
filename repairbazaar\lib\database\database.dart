import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:sqlite3/sqlite3.dart';
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

import 'tables.dart';

part 'database.g.dart';

@DriftDatabase(tables: [
  Customers,
  Technicians,
  Inventory,
  Jobs,
  JobParts,
  Payments,
  JobStatusHistory,
  DevicePhotos,
])
class RepairDatabase extends _$RepairDatabase {
  RepairDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
        
        // Insert default technician (shop owner)
        await into(technicians).insert(TechniciansCompanion.insert(
          name: 'Shop Owner',
          phone: '**********',
          specialization: const Value('All Devices'),
        ));
        
        // Insert some sample inventory categories
        final sampleParts = [
          InventoryCompanion.insert(
            partName: 'iPhone 14 Screen',
            category: 'Screen',
            brand: const Value('Apple'),
            model: const Value('iPhone 14'),
            quantity: const Value(5),
            costPrice: 8000.0,
            sellingPrice: 12000.0,
          ),
          InventoryCompanion.insert(
            partName: 'Samsung Galaxy S23 Battery',
            category: 'Battery',
            brand: const Value('Samsung'),
            model: const Value('Galaxy S23'),
            quantity: const Value(3),
            costPrice: 2000.0,
            sellingPrice: 3500.0,
          ),
          InventoryCompanion.insert(
            partName: 'Universal Phone Charger',
            category: 'Accessories',
            quantity: const Value(10),
            costPrice: 200.0,
            sellingPrice: 500.0,
          ),
        ];
        
        for (final part in sampleParts) {
          await into(inventory).insert(part);
        }
      },
      onUpgrade: (Migrator m, int from, int to) async {
        // Handle database upgrades here
      },
    );
  }

  // Customer queries
  Future<List<Customer>> getAllCustomers() => select(customers).get();
  
  Future<Customer?> getCustomerById(int id) => 
      (select(customers)..where((c) => c.id.equals(id))).getSingleOrNull();
  
  Future<List<Customer>> searchCustomers(String query) =>
      (select(customers)..where((c) => 
          c.name.contains(query) | c.phone.contains(query))).get();

  Future<int> insertCustomer(CustomersCompanion customer) =>
      into(customers).insert(customer);

  Future<bool> updateCustomer(CustomersCompanion customer) =>
      update(customers).replace(customer);

  // Technician queries
  Future<List<Technician>> getAllTechnicians() => 
      (select(technicians)..where((t) => t.isActive.equals(true))).get();
  
  Future<Technician?> getTechnicianById(int id) =>
      (select(technicians)..where((t) => t.id.equals(id))).getSingleOrNull();

  Future<int> insertTechnician(TechniciansCompanion technician) =>
      into(technicians).insert(technician);

  // Inventory queries
  Future<List<InventoryData>> getAllInventory() => select(inventory).get();
  
  Future<List<InventoryData>> getLowStockItems() =>
      (select(inventory)..where((i) => i.quantity.isSmallerOrEqual(i.minStockLevel))).get();
  
  Future<List<InventoryData>> searchInventory(String query) =>
      (select(inventory)..where((i) => 
          i.partName.contains(query) | 
          i.category.contains(query) |
          i.brand.contains(query))).get();

  Future<int> insertInventoryItem(InventoryCompanion item) =>
      into(inventory).insert(item);

  Future<bool> updateInventoryQuantity(int partId, int newQuantity) async {
    final result = await (update(inventory)..where((i) => i.id.equals(partId)))
        .write(InventoryCompanion(quantity: Value(newQuantity)));
    return result > 0;
  }

  // Job queries
  Future<List<Job>> getAllJobs() =>
      (select(jobs)..orderBy([(j) => OrderingTerm(expression: j.createdAt, mode: OrderingMode.desc)])).get();
  
  Future<List<Job>> getJobsByStatus(String status) =>
      (select(jobs)..where((j) => j.status.equals(status))
       ..orderBy([(j) => OrderingTerm(expression: j.createdAt, mode: OrderingMode.desc)])).get();
  
  Future<Job?> getJobById(int id) =>
      (select(jobs)..where((j) => j.id.equals(id))).getSingleOrNull();
  
  Future<Job?> getJobByNumber(String jobNumber) =>
      (select(jobs)..where((j) => j.jobNumber.equals(jobNumber))).getSingleOrNull();

  Future<List<Job>> searchJobs(String query) =>
      (select(jobs)..where((j) => 
          j.jobNumber.contains(query) |
          j.deviceModel.contains(query) |
          j.imei.contains(query))).get();

  Future<int> insertJob(JobsCompanion job) => into(jobs).insert(job);

  Future<bool> updateJobStatus(int jobId, String newStatus) async {
    final result = await (update(jobs)..where((j) => j.id.equals(jobId)))
        .write(JobsCompanion(status: Value(newStatus), updatedAt: Value(DateTime.now())));
    return result > 0;
  }

  // Generate unique job number
  Future<String> generateJobNumber() async {
    final now = DateTime.now();
    final prefix = 'RB${now.year}${now.month.toString().padLeft(2, '0')}';
    
    final lastJob = await (select(jobs)
        ..where((j) => j.jobNumber.like('$prefix%'))
        ..orderBy([(j) => OrderingTerm.desc(j.id)])
        ..limit(1)).getSingleOrNull();
    
    int nextNumber = 1;
    if (lastJob != null) {
      final lastNumber = int.tryParse(lastJob.jobNumber.substring(prefix.length)) ?? 0;
      nextNumber = lastNumber + 1;
    }
    
    return '$prefix${nextNumber.toString().padLeft(4, '0')}';
  }

  // Job Parts queries
  Future<List<JobPart>> getJobParts(int jobId) =>
      (select(jobParts)..where((jp) => jp.jobId.equals(jobId))).get();

  Future<int> insertJobPart(JobPartsCompanion jobPart) =>
      into(jobParts).insert(jobPart);

  // Payment queries
  Future<List<Payment>> getJobPayments(int jobId) =>
      (select(payments)..where((p) => p.jobId.equals(jobId))).get();

  Future<int> insertPayment(PaymentsCompanion payment) =>
      into(payments).insert(payment);

  // Analytics queries
  Future<double> getTodaysEarnings() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final result = await (selectOnly(payments)
        ..addColumns([payments.amount.sum()])
        ..where(payments.paidAt.isBetweenValues(startOfDay, endOfDay))).getSingle();
    
    return result.read(payments.amount.sum()) ?? 0.0;
  }

  Future<int> getTodaysCompletedJobs() async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    return await (selectOnly(jobs)
        ..addColumns([jobs.id.count()])
        ..where(jobs.completedAt.isBetweenValues(startOfDay, endOfDay))).getSingle()
        .then((row) => row.read(jobs.id.count()) ?? 0);
  }

  Future<int> getPendingJobsCount() async {
    return await (selectOnly(jobs)
        ..addColumns([jobs.id.count()])
        ..where(jobs.status.isIn(['pending', 'diagnosed', 'in_progress', 'waiting_parts']))).getSingle()
        .then((row) => row.read(jobs.id.count()) ?? 0);
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'repair_shop.db'));
    
    // Make sure sqlite3 is properly initialized
    if (Platform.isAndroid) {
      await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
    }
    
    final cachebase = (await getTemporaryDirectory()).path;
    sqlite3.tempDirectory = cachebase;
    
    return NativeDatabase.createInBackground(file);
  });
}

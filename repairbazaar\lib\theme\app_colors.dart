import 'package:flutter/material.dart';

class AppColors {
  // Netflix-inspired color palette
  
  // Primary Background Colors
  static const Color primaryBlack = Color(0xFF000000);
  static const Color charcoalBlack = Color(0xFF141414);
  static const Color darkGray = Color(0xFF1F1F1F);
  static const Color mediumGray = Color(0xFF2F2F2F);
  static const Color lightGray = Color(0xFF404040);
  
  // Netflix Red Accent
  static const Color netflixRed = Color(0xFFE50914);
  static const Color darkRed = Color(0xFFB20710);
  static const Color lightRed = Color(0xFFFF1E2D);
  
  // Electric Accent (Alternative)
  static const Color electricBlue = Color(0xFF00D4FF);
  static const Color electricPurple = Color(0xFF8A2BE2);
  
  // Text Colors
  static const Color primaryText = Color(0xFFFFFFFF);
  static const Color secondaryText = Color(0xFFB3B3B3);
  static const Color mutedText = Color(0xFF808080);
  static const Color disabledText = Color(0xFF4D4D4D);
  
  // Status Colors
  static const Color successGreen = Color(0xFF46D369);
  static const Color warningAmber = Color(0xFFFFA726);
  static const Color errorRed = Color(0xFFFF5252);
  static const Color infoBlue = Color(0xFF42A5F5);
  
  // Card and Surface Colors
  static const Color cardBackground = Color(0xFF1A1A1A);
  static const Color surfaceColor = Color(0xFF262626);
  static const Color overlayColor = Color(0x80000000);
  
  // Gradient Colors
  static const LinearGradient heroGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x00000000),
      Color(0x80000000),
      Color(0xFF000000),
    ],
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF1F1F1F),
      Color(0xFF141414),
    ],
  );
  
  static const LinearGradient redGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      netflixRed,
      darkRed,
    ],
  );
  
  // Status-specific colors
  static const Map<String, Color> statusColors = {
    'pending': warningAmber,
    'diagnosed': infoBlue,
    'in_progress': electricBlue,
    'waiting_parts': warningAmber,
    'completed': successGreen,
    'delivered': successGreen,
    'cancelled': errorRed,
  };
  
  // Category colors for inventory
  static const Map<String, Color> categoryColors = {
    'Screen': Color(0xFF4FC3F7),
    'Battery': Color(0xFF66BB6A),
    'Camera': Color(0xFFFFB74D),
    'Speaker': Color(0xFFBA68C8),
    'Charging Port': Color(0xFFFF8A65),
    'Accessories': Color(0xFF81C784),
    'Tools': Color(0xFF90A4AE),
    'Other': mutedText,
  };
  
  // Device type colors
  static const Map<String, Color> deviceTypeColors = {
    'Phone': Color(0xFF42A5F5),
    'Tablet': Color(0xFF66BB6A),
    'Laptop': Color(0xFFFFB74D),
    'Watch': Color(0xFFBA68C8),
    'Headphones': Color(0xFFFF8A65),
    'Other': mutedText,
  };
  
  // Shimmer colors for loading states
  static const Color shimmerBase = Color(0xFF1F1F1F);
  static const Color shimmerHighlight = Color(0xFF2F2F2F);
  
  // Border colors
  static const Color borderColor = Color(0xFF333333);
  static const Color focusedBorderColor = netflixRed;
  static const Color errorBorderColor = errorRed;
  
  // Button colors
  static const Color primaryButtonColor = netflixRed;
  static const Color secondaryButtonColor = mediumGray;
  static const Color disabledButtonColor = Color(0xFF1A1A1A);
  
  // Input field colors
  static const Color inputFillColor = Color(0xFF1A1A1A);
  static const Color inputBorderColor = Color(0xFF333333);
  static const Color inputFocusedBorderColor = netflixRed;
  
  // Divider colors
  static const Color dividerColor = Color(0xFF333333);
  static const Color subtleDividerColor = Color(0xFF262626);
  
  // Shadow colors
  static const Color shadowColor = Color(0x40000000);
  static const Color cardShadowColor = Color(0x60000000);
  
  // Helper method to get status color
  static Color getStatusColor(String status) {
    return statusColors[status.toLowerCase()] ?? mutedText;
  }
  
  // Helper method to get category color
  static Color getCategoryColor(String category) {
    return categoryColors[category] ?? mutedText;
  }
  
  // Helper method to get device type color
  static Color getDeviceTypeColor(String deviceType) {
    return deviceTypeColors[deviceType] ?? mutedText;
  }
  
  // Helper method to create a subtle glow effect
  static BoxShadow createGlow({
    Color color = netflixRed,
    double blurRadius = 8.0,
    double spreadRadius = 0.0,
    Offset offset = Offset.zero,
  }) {
    return BoxShadow(
      color: color.withOpacity(0.3),
      blurRadius: blurRadius,
      spreadRadius: spreadRadius,
      offset: offset,
    );
  }
  
  // Helper method to create card shadow
  static List<BoxShadow> createCardShadow() {
    return [
      BoxShadow(
        color: cardShadowColor,
        blurRadius: 12.0,
        spreadRadius: 0.0,
        offset: const Offset(0, 4),
      ),
      BoxShadow(
        color: shadowColor,
        blurRadius: 6.0,
        spreadRadius: 0.0,
        offset: const Offset(0, 2),
      ),
    ];
  }
}

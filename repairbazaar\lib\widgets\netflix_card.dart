import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

class NetflixCard extends StatefulWidget {
  final Widget? thumbnail;
  final String title;
  final String? subtitle;
  final String? status;
  final List<Widget>? quickActions;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final double? width;
  final double? height;
  final bool showOverlay;
  final Widget? badge;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;

  const NetflixCard({
    super.key,
    this.thumbnail,
    required this.title,
    this.subtitle,
    this.status,
    this.quickActions,
    this.onTap,
    this.onLongPress,
    this.width,
    this.height = 200,
    this.showOverlay = true,
    this.badge,
    this.margin,
    this.padding,
  });

  @override
  State<NetflixCard> createState() => _NetflixCardState();
}

class _NetflixCardState extends State<NetflixCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _overlayAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _overlayAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHoverStart() {
    setState(() => _isHovered = true);
    _animationController.forward();
  }

  void _onHoverEnd() {
    setState(() => _isHovered = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      margin: widget.margin ?? const EdgeInsets.all(8),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: MouseRegion(
              onEnter: (_) => _onHoverStart(),
              onExit: (_) => _onHoverEnd(),
              child: GestureDetector(
                onTap: widget.onTap,
                onLongPress: widget.onLongPress,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: AppColors.cardGradient,
                    boxShadow: _isHovered 
                        ? AppColors.createCardShadow()
                        : [
                            BoxShadow(
                              color: AppColors.shadowColor,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Stack(
                      children: [
                        // Thumbnail/Background
                        if (widget.thumbnail != null)
                          Positioned.fill(
                            child: widget.thumbnail!,
                          )
                        else
                          Positioned.fill(
                            child: Container(
                              decoration: const BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppColors.mediumGray,
                                    AppColors.darkGray,
                                  ],
                                ),
                              ),
                              child: const Icon(
                                Icons.image,
                                size: 48,
                                color: AppColors.mutedText,
                              ),
                            ),
                          ),
                        
                        // Gradient overlay
                        if (widget.showOverlay)
                          Positioned.fill(
                            child: Container(
                              decoration: const BoxDecoration(
                                gradient: AppColors.heroGradient,
                              ),
                            ),
                          ),
                        
                        // Content
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0,
                          child: Container(
                            padding: widget.padding ?? const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Title
                                Text(
                                  widget.title,
                                  style: AppTypography.cardTitle,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                
                                // Subtitle
                                if (widget.subtitle != null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    widget.subtitle!,
                                    style: AppTypography.cardSubtitle,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                                
                                // Status
                                if (widget.status != null) ...[
                                  const SizedBox(height: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppColors.getStatusColor(widget.status!),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      widget.status!.toUpperCase(),
                                      style: AppTypography.statusBadge,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                        
                        // Badge (top-right)
                        if (widget.badge != null)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: widget.badge!,
                          ),
                        
                        // Quick actions overlay (appears on hover)
                        if (widget.quickActions != null && widget.showOverlay)
                          AnimatedBuilder(
                            animation: _overlayAnimation,
                            builder: (context, child) {
                              return Positioned.fill(
                                child: Opacity(
                                  opacity: _overlayAnimation.value,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: AppColors.overlayColor,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Center(
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                        children: widget.quickActions!,
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Quick action button for overlay
class QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final Color? color;

  const QuickActionButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onPressed,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          decoration: BoxDecoration(
            color: color ?? AppColors.netflixRed,
            shape: BoxShape.circle,
            boxShadow: [
              AppColors.createGlow(color: color ?? AppColors.netflixRed),
            ],
          ),
          child: IconButton(
            onPressed: onPressed,
            icon: Icon(icon),
            color: AppColors.primaryText,
            iconSize: 20,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTypography.labelSmall.copyWith(
            color: AppColors.primaryText,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

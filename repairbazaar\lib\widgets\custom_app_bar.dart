import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

// Search query provider
final searchQueryProvider = StateProvider<String>((ref) => '');

// Search focus provider
final searchFocusProvider = StateProvider<bool>((ref) => false);

class CustomAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const CustomAppBar({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final searchQuery = ref.watch(searchQueryProvider);
    final isSearchFocused = ref.watch(searchFocusProvider);

    return AppBar(
      backgroundColor: AppColors.charcoalBlack,
      elevation: 0,
      scrolledUnderElevation: 4,
      shadowColor: AppColors.shadowColor,
      surfaceTintColor: Colors.transparent,
      leading: _buildLogo(),
      title: _buildSearchBar(context, ref, searchQuery, isSearchFocused),
      actions: _buildActions(context),
    );
  }

  Widget _buildLogo() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: const BoxDecoration(
              gradient: AppColors.redGradient,
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.build,
              color: AppColors.primaryText,
              size: 18,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'RB',
            style: AppTypography.titleLarge,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context, WidgetRef ref, String searchQuery, bool isSearchFocused) {
    return Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.inputFillColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isSearchFocused ? AppColors.netflixRed : AppColors.borderColor,
          width: isSearchFocused ? 2 : 1,
        ),
      ),
      child: TextField(
        onChanged: (value) => ref.read(searchQueryProvider.notifier).state = value,
        onTap: () => ref.read(searchFocusProvider.notifier).state = true,
        onEditingComplete: () => ref.read(searchFocusProvider.notifier).state = false,
        onSubmitted: (value) {
          ref.read(searchFocusProvider.notifier).state = false;
          _performSearch(context, value);
        },
        style: AppTypography.bodyMedium,
        decoration: InputDecoration(
          hintText: 'Search jobs, customers, IMEI...',
          hintStyle: AppTypography.bodyMedium.copyWith(
            color: AppColors.mutedText,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: AppColors.mutedText,
            size: 20,
          ),
          suffixIcon: searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    ref.read(searchQueryProvider.notifier).state = '';
                    ref.read(searchFocusProvider.notifier).state = false;
                  },
                  icon: const Icon(
                    Icons.clear,
                    color: AppColors.mutedText,
                    size: 20,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 10,
          ),
        ),
      ),
    );
  }

  List<Widget> _buildActions(BuildContext context) {
    return [
      // WhatsApp quick action
      IconButton(
        onPressed: () => _openWhatsApp(),
        icon: const Icon(Icons.chat),
        color: AppColors.successGreen,
        tooltip: 'WhatsApp',
      ),
      
      // Notifications
      Stack(
        children: [
          IconButton(
            onPressed: () => _showNotifications(context),
            icon: const Icon(Icons.notifications_outlined),
            color: AppColors.primaryText,
            tooltip: 'Notifications',
          ),
          // Notification badge
          Positioned(
            right: 8,
            top: 8,
            child: Container(
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: AppColors.netflixRed,
                shape: BoxShape.circle,
              ),
            ),
          ),
        ],
      ),
      
      // More options
      PopupMenuButton<String>(
        onSelected: (value) => _handleMenuAction(context, value),
        icon: const Icon(
          Icons.more_vert,
          color: AppColors.primaryText,
        ),
        color: AppColors.cardBackground,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'settings',
            child: ListTile(
              leading: Icon(Icons.settings, color: AppColors.primaryText),
              title: Text('Settings', style: AppTypography.bodyMedium),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'backup',
            child: ListTile(
              leading: Icon(Icons.backup, color: AppColors.primaryText),
              title: Text('Backup Data', style: AppTypography.bodyMedium),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'help',
            child: ListTile(
              leading: Icon(Icons.help, color: AppColors.primaryText),
              title: Text('Help & Support', style: AppTypography.bodyMedium),
              contentPadding: EdgeInsets.zero,
            ),
          ),
          const PopupMenuItem(
            value: 'about',
            child: ListTile(
              leading: Icon(Icons.info, color: AppColors.primaryText),
              title: Text('About', style: AppTypography.bodyMedium),
              contentPadding: EdgeInsets.zero,
            ),
          ),
        ],
      ),
      
      const SizedBox(width: 8),
    ];
  }

  void _performSearch(BuildContext context, String query) {
    if (query.trim().isEmpty) return;
    
    // TODO: Implement search functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Searching for: $query'),
        backgroundColor: AppColors.cardBackground,
      ),
    );
  }

  void _openWhatsApp() async {
    // Open WhatsApp with a default message
    const phoneNumber = ''; // Add default phone number if needed
    const message = 'Hello! I have a query about my device repair.';
    final url = 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';
    
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    }
  }

  void _showNotifications(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.cardBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Notifications',
                  style: AppTypography.headlineSmall,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Clear All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const ListTile(
              leading: Icon(Icons.info, color: AppColors.infoBlue),
              title: Text('Welcome to RepairBazaar!', style: AppTypography.titleMedium),
              subtitle: Text('Start managing your repair shop efficiently.', style: AppTypography.bodySmall),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'settings':
        // TODO: Navigate to settings
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings coming soon!')),
        );
        break;
      case 'backup':
        // TODO: Implement backup
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Backup feature coming soon!')),
        );
        break;
      case 'help':
        // TODO: Show help
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Help & Support coming soon!')),
        );
        break;
      case 'about':
        _showAboutDialog(context);
        break;
    }
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.cardBackground,
        title: const Text('About RepairBazaar', style: AppTypography.headlineSmall),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Version 1.0.0', style: AppTypography.bodyMedium),
            SizedBox(height: 8),
            Text(
              'Netflix-inspired Mobile Repair Shop Management App',
              style: AppTypography.bodySmall,
            ),
            SizedBox(height: 8),
            Text(
              'Fully offline with SQLite database',
              style: AppTypography.bodySmall,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

{"elements": {"RepairDatabase": {"id": {"library_uri": "package:repairbazaar/database/database.dart", "name": "RepairDatabase"}, "declaration": {"source_uri": "package:repairbazaar/database/database.dart", "offset": 474, "name": "RepairDatabase"}, "references": [{"library_uri": "package:repairbazaar/database/tables.dart", "name": "customers"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "technicians"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "inventory"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "job_parts"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "payments"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "job_status_history"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "device_photos"}], "type": "database", "tables": [{"library_uri": "package:repairbazaar/database/tables.dart", "name": "customers"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "technicians"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "inventory"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "jobs"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "job_parts"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "payments"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "job_status_history"}, {"library_uri": "package:repairbazaar/database/tables.dart", "name": "device_photos"}], "views": [], "includes": [], "queries": [], "schema_version": 1, "daos": [], "has_constructor_arg": false}}, "imports": ["package:drift/drift.dart", "package:drift/native.dart", "package:path_provider/path_provider.dart", "package:path/path.dart", "package:sqlite3/sqlite3.dart", "package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart", "package:repairbazaar/database/tables.dart"]}
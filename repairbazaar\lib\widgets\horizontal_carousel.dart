import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

class HorizontalCarousel extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget> items;
  final double itemWidth;
  final double itemHeight;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onSeeAll;
  final bool showSeeAll;
  final ScrollController? scrollController;

  const HorizontalCarousel({
    super.key,
    required this.title,
    this.subtitle,
    required this.items,
    this.itemWidth = 200,
    this.itemHeight = 200,
    this.padding,
    this.onSeeAll,
    this.showSeeAll = true,
    this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTypography.headlineMedium,
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle!,
                          style: AppTypography.bodySmall,
                        ),
                      ],
                    ],
                  ),
                ),
                if (showSeeAll && onSeeAll != null)
                  TextButton(
                    onPressed: onSeeAll,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'See All',
                          style: AppTypography.labelLarge.copyWith(
                            color: AppColors.netflixRed,
                          ),
                        ),
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: AppColors.netflixRed,
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Carousel
          SizedBox(
            height: itemHeight,
            child: items.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    controller: scrollController,
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    itemCount: items.length,
                    itemBuilder: (context, index) {
                      return SizedBox(
                        width: itemWidth,
                        child: items[index],
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.borderColor,
          style: BorderStyle.solid,
          width: 1,
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 48,
              color: AppColors.mutedText,
            ),
            SizedBox(height: 8),
            Text(
              'No items to display',
              style: AppTypography.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}

// Specialized carousel for different content types
class JobCarousel extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget> jobs;
  final VoidCallback? onSeeAll;

  const JobCarousel({
    super.key,
    required this.title,
    this.subtitle,
    required this.jobs,
    this.onSeeAll,
  });

  @override
  Widget build(BuildContext context) {
    return HorizontalCarousel(
      title: title,
      subtitle: subtitle,
      items: jobs,
      itemWidth: 280,
      itemHeight: 200,
      onSeeAll: onSeeAll,
    );
  }
}

class InventoryCarousel extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget> items;
  final VoidCallback? onSeeAll;

  const InventoryCarousel({
    super.key,
    required this.title,
    this.subtitle,
    required this.items,
    this.onSeeAll,
  });

  @override
  Widget build(BuildContext context) {
    return HorizontalCarousel(
      title: title,
      subtitle: subtitle,
      items: items,
      itemWidth: 200,
      itemHeight: 160,
      onSeeAll: onSeeAll,
    );
  }
}

class TechnicianCarousel extends StatelessWidget {
  final String title;
  final String? subtitle;
  final List<Widget> technicians;
  final VoidCallback? onSeeAll;

  const TechnicianCarousel({
    super.key,
    required this.title,
    this.subtitle,
    required this.technicians,
    this.onSeeAll,
  });

  @override
  Widget build(BuildContext context) {
    return HorizontalCarousel(
      title: title,
      subtitle: subtitle,
      items: technicians,
      itemWidth: 160,
      itemHeight: 180,
      onSeeAll: onSeeAll,
    );
  }
}

// Carousel with loading state
class LoadingCarousel extends StatelessWidget {
  final String title;
  final String? subtitle;
  final int itemCount;
  final double itemWidth;
  final double itemHeight;

  const LoadingCarousel({
    super.key,
    required this.title,
    this.subtitle,
    this.itemCount = 5,
    this.itemWidth = 200,
    this.itemHeight = 200,
  });

  @override
  Widget build(BuildContext context) {
    return HorizontalCarousel(
      title: title,
      subtitle: subtitle,
      items: List.generate(
        itemCount,
        (index) => _buildShimmerCard(),
      ),
      itemWidth: itemWidth,
      itemHeight: itemHeight,
      showSeeAll: false,
    );
  }

  Widget _buildShimmerCard() {
    return Container(
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.shimmerBase,
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // Shimmer effect
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(-1.0, -0.3),
                    end: Alignment(1.0, 0.3),
                    colors: [
                      AppColors.shimmerBase,
                      AppColors.shimmerHighlight,
                      AppColors.shimmerBase,
                    ],
                    stops: [0.0, 0.5, 1.0],
                  ),
                ),
              ),
            ),
            
            // Content placeholders
            Positioned(
              left: 16,
              right: 16,
              bottom: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 16,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: AppColors.shimmerHighlight,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 12,
                    width: 120,
                    decoration: BoxDecoration(
                      color: AppColors.shimmerHighlight,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 20,
                    width: 80,
                    decoration: BoxDecoration(
                      color: AppColors.shimmerHighlight,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

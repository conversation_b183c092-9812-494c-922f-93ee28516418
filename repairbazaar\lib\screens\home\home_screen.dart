import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_typography.dart';
import '../../widgets/hero_banner.dart';
import '../../widgets/horizontal_carousel.dart';
import '../../widgets/netflix_card.dart';
import '../../providers/database_provider.dart';

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.primaryBlack,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Hero Banner
            const WelcomeHeroBanner(
              userName: 'Shop Owner',
              message: 'Manage your repair shop with ease',
            ),
            
            // Quick Stats
            _buildQuickStats(ref),
            
            // Active Repairs Carousel
            _buildActiveRepairsCarousel(ref),
            
            // Today's Earnings
            _buildTodaysEarnings(ref),
            
            // Low Stock Alert
            _buildLowStockAlert(ref),
            
            // Technician Highlights
            _buildTechnicianHighlights(ref),
            
            const SizedBox(height: 100), // Bottom padding for navigation
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(WidgetRef ref) {
    final pendingJobsAsync = ref.watch(pendingJobsCountProvider);
    final todaysEarningsAsync = ref.watch(todaysEarningsProvider);
    final todaysCompletedAsync = ref.watch(todaysCompletedJobsProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: pendingJobsAsync.when(
              data: (count) => StatsHeroBanner(
                title: 'Pending Jobs',
                value: count.toString(),
                icon: Icons.pending_actions,
                color: AppColors.warningAmber,
              ),
              loading: () => const _LoadingStatsBanner(),
              error: (_, __) => const _ErrorStatsBanner(),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: todaysEarningsAsync.when(
              data: (earnings) => StatsHeroBanner(
                title: 'Today\'s Earnings',
                value: '₹${earnings.toStringAsFixed(0)}',
                icon: Icons.currency_rupee,
                color: AppColors.successGreen,
              ),
              loading: () => const _LoadingStatsBanner(),
              error: (_, __) => const _ErrorStatsBanner(),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: todaysCompletedAsync.when(
              data: (count) => StatsHeroBanner(
                title: 'Completed Today',
                value: count.toString(),
                icon: Icons.check_circle,
                color: AppColors.infoBlue,
              ),
              loading: () => const _LoadingStatsBanner(),
              error: (_, __) => const _ErrorStatsBanner(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveRepairsCarousel(WidgetRef ref) {
    final activeJobsAsync = ref.watch(activeJobsProvider);

    return activeJobsAsync.when(
      data: (jobs) => JobCarousel(
        title: 'Active Repairs',
        subtitle: '${jobs.length} jobs in progress',
        jobs: jobs.take(10).map((job) => NetflixCard(
          title: '${job.deviceBrand} ${job.deviceModel}',
          subtitle: job.issueDescription,
          status: job.status,
          thumbnail: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  AppColors.getDeviceTypeColor(job.deviceType),
                  AppColors.getDeviceTypeColor(job.deviceType).withOpacity(0.7),
                ],
              ),
            ),
            child: Icon(
              _getDeviceIcon(job.deviceType),
              size: 48,
              color: AppColors.primaryText,
            ),
          ),
          quickActions: [
            QuickActionButton(
              icon: Icons.check,
              label: 'Complete',
              onPressed: () => _markJobComplete(ref, job.id),
            ),
            QuickActionButton(
              icon: Icons.person,
              label: 'Assign',
              onPressed: () => _assignTechnician(job.id),
            ),
            QuickActionButton(
              icon: Icons.message,
              label: 'Message',
              onPressed: () => _messageCustomer(job.id),
            ),
          ],
          onTap: () => _viewJobDetails(job.id),
        )).toList(),
        onSeeAll: () => _navigateToOrders(),
      ),
      loading: () => const LoadingCarousel(
        title: 'Active Repairs',
        subtitle: 'Loading...',
        itemWidth: 280,
        itemHeight: 200,
      ),
      error: (_, __) => const HorizontalCarousel(
        title: 'Active Repairs',
        subtitle: 'Error loading jobs',
        items: [],
        itemWidth: 280,
        itemHeight: 200,
      ),
    );
  }

  Widget _buildTodaysEarnings(WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: const StatsHeroBanner(
        title: 'Revenue Trend',
        value: '↗ +12%',
        subtitle: 'Compared to yesterday',
        icon: Icons.trending_up,
        color: AppColors.successGreen,
      ),
    );
  }

  Widget _buildLowStockAlert(WidgetRef ref) {
    final lowStockAsync = ref.watch(lowStockItemsProvider);

    return lowStockAsync.when(
      data: (items) => items.isNotEmpty
          ? AlertHeroBanner(
              title: '${items.length} Items Low in Stock',
              description: 'Restock needed: ${items.map((e) => e.partName).take(3).join(', ')}',
              icon: Icons.warning,
              actionLabel: 'View Inventory',
              onAction: () => _navigateToInventory(),
            )
          : const SizedBox.shrink(),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildTechnicianHighlights(WidgetRef ref) {
    final techniciansAsync = ref.watch(techniciansProvider);

    return techniciansAsync.when(
      data: (technicians) => TechnicianCarousel(
        title: 'Technician Highlights',
        subtitle: '${technicians.length} active technicians',
        technicians: technicians.map((tech) => NetflixCard(
          title: tech.name,
          subtitle: tech.specialization ?? 'General',
          width: 160,
          height: 180,
          thumbnail: Container(
            decoration: const BoxDecoration(
              gradient: AppColors.cardGradient,
            ),
            child: const Icon(
              Icons.person,
              size: 48,
              color: AppColors.primaryText,
            ),
          ),
          badge: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: AppColors.successGreen,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'ACTIVE',
              style: AppTypography.labelSmall,
            ),
          ),
          onTap: () => _viewTechnicianDetails(tech.id),
        )).toList(),
      ),
      loading: () => const LoadingCarousel(
        title: 'Technician Highlights',
        subtitle: 'Loading...',
        itemWidth: 160,
        itemHeight: 180,
      ),
      error: (_, __) => const TechnicianCarousel(
        title: 'Technician Highlights',
        subtitle: 'Error loading technicians',
        technicians: [],
      ),
    );
  }

  IconData _getDeviceIcon(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'phone':
        return Icons.smartphone;
      case 'tablet':
        return Icons.tablet;
      case 'laptop':
        return Icons.laptop;
      case 'watch':
        return Icons.watch;
      default:
        return Icons.devices;
    }
  }

  void _markJobComplete(WidgetRef ref, int jobId) {
    // TODO: Implement mark job complete
  }

  void _assignTechnician(int jobId) {
    // TODO: Implement assign technician
  }

  void _messageCustomer(int jobId) {
    // TODO: Implement message customer
  }

  void _viewJobDetails(int jobId) {
    // TODO: Navigate to job details
  }

  void _navigateToOrders() {
    // TODO: Navigate to orders tab
  }

  void _navigateToInventory() {
    // TODO: Navigate to inventory tab
  }

  void _viewTechnicianDetails(int techId) {
    // TODO: Navigate to technician details
  }
}

class _LoadingStatsBanner extends StatelessWidget {
  const _LoadingStatsBanner();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 140,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.shimmerBase,
        borderRadius: BorderRadius.circular(16),
      ),
    );
  }
}

class _ErrorStatsBanner extends StatelessWidget {
  const _ErrorStatsBanner();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 140,
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.errorRed),
      ),
      child: const Center(
        child: Icon(
          Icons.error_outline,
          color: AppColors.errorRed,
        ),
      ),
    );
  }
}
